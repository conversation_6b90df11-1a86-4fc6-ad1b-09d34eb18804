body {
  color: #313131;
}

img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.banner {
  height: 175px;
  overflow: hidden;
  border-radius: 8px;
  margin-bottom: 1rem;
}
.banner-skeleton {
  width: 100%;
  height: 175px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f3f3f3;
  border-radius: 8px;
  overflow: hidden;
}

.skeleton-img {
  width: 100%;
  height: 100%;
  background: linear-gradient(-90deg, #f0f0f0 0%, #e0e0e0 50%, #f0f0f0 100%);
  background-size: 200% 100%;
  animation: loading 1.2s ease-in-out infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.animate-pulse {
  animation: loading 1.5s infinite linear;
}

.section-title {
  font-weight: bold;
  text-transform: uppercase;
  margin-top: 2rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
}

.lihat-semua {
  display: flex;
  align-items: center;
  font-size: 12px;
  gap: 0.25rem;
}

.grid {
  display: grid;
  gap: 1rem;
  margin-top: 1rem;
}

.kategori {
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
}

.produk-terlaris,
.produk-rating,
.produk-terbaik {
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
}

.card {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.card-img {
  height: 150px;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 14px;
  text-transform: capitalize;
}

.card-price {
  font-weight: bold;
  color: black;
}

.card-meta {
  font-size: 10px;
  color: #838383;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.main-layout {
  margin-left: auto;
  margin-right: auto;
  width: 100%;
  margin-top: 1.25rem; /* mt-5 */
  padding-left: 1.25rem; /* px-5 */
  padding-right: 1.25rem;
  margin-bottom: 120px;
}

/* sm:mt-3 */
@media (min-width: 640px) {
  .main-layout {
    margin-top: 1.25rem;
    max-width: 700px;
  }
  .banner {
    height: 200px;
  }
  .banner-skeleton {
    height: 200px;
  }
}

/* md:px-0 */
@media (min-width: 768px) {
  .main-layout {
    margin-top: 2rem;
    padding-left: 0;
    padding-right: 0;
  }
}

/* xl:max-w-[1140px] */
@media (min-width: 1280px) {
  .main-layout {
    max-width: 1140px;
  }
  .banner {
    height: 350px;
  }

  .banner-skeleton {
    height: 300px;
  }
}

.banner-swiper-pagination {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
}

.swiper,
.banner {
  position: relative;
}
